import React from 'react';
import { Card } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft, Copy, Download } from 'lucide-react';
import Header from '@/components/Header';

const AlignmentVerification = () => {
  // Mock data to simulate the exact TranslationViewer state
  const mockFile = {
    name: "Sample_RFP_Document_Very_Long_Filename_Test.pdf"
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      <Header title="Alignment Verification - Fixed TranslationViewer">
        <div className="flex items-center gap-4">
          <Button size="sm" variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Home
          </Button>
        </div>
      </Header>

      {/* Content - Exact copy of TranslationViewer structure with fixes applied */}
      <div className="max-w-[95vw] mx-auto p-4">
        <div className="grid lg:grid-cols-2 gap-4">
          {/* Original PDF Panel - FIXED */}
          <Card className="bg-slate-800/50 backdrop-blur-sm border-slate-700 p-4 transition-all duration-200 hover:bg-slate-800/60">
            <div className="space-y-4">
              {/* FIXED: Changed from min-h-[40px] to min-h-[60px], removed min-h-[36px] from filename container */}
              <div className="flex items-center justify-between min-h-[60px] bg-green-500/10 border border-green-500/30">
                <div className="flex items-center bg-yellow-500/10 border border-yellow-500/30">
                  <h2 className="text-xl font-semibold text-slate-200">Original Document</h2>
                </div>
                <div className="flex items-center gap-2 max-w-[300px] bg-blue-500/10 border border-blue-500/30">
                  <div className="bg-slate-700/50 rounded-lg px-3 py-2 border border-slate-600 flex items-center">
                    <span className="text-sm text-slate-300 font-medium truncate" title={mockFile.name}>
                      {mockFile.name}
                    </span>
                  </div>
                </div>
              </div>

              {/* Document Info */}
              <div className="flex items-center justify-end py-2">
                <span className="text-slate-400 text-sm">12 pages</span>
              </div>
              
              {/* PDF Viewer Mock */}
              <div className="bg-slate-900/50 rounded-lg p-4 min-h-[400px] flex items-center justify-center">
                <div className="text-slate-400 text-center">
                  <div className="text-6xl mb-4">📄</div>
                  <p>PDF Viewer</p>
                  <p className="text-sm mt-2">Original document displayed here</p>
                </div>
              </div>
            </div>
          </Card>

          {/* Translated Document Panel - FIXED */}
          <Card className="bg-slate-800/50 backdrop-blur-sm border-slate-700 p-4 transition-all duration-200 hover:bg-slate-800/60">
            <div className="space-y-4">
              {/* FIXED: Changed from min-h-[40px] to min-h-[60px], removed min-h-[36px] from buttons container */}
              <div className="flex items-center justify-between min-h-[60px] bg-green-500/10 border border-green-500/30">
                <div className="flex items-center bg-yellow-500/10 border border-yellow-500/30">
                  <h2 className="text-xl font-semibold text-slate-200">Translated Document</h2>
                </div>
                <div className="flex items-center gap-2 bg-blue-500/10 border border-blue-500/30">
                  <Button size="sm" variant="outline">
                    <Copy className="h-4 w-4 mr-2" />
                    Copy
                  </Button>
                  <Button size="sm" variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Export HTML
                  </Button>
                </div>
              </div>

              {/* Translated Document Info */}
              <div className="flex items-center justify-between py-2">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span className="text-green-400 text-sm font-medium">Translation Complete</span>
                </div>
                <span className="text-slate-400 text-sm">1,247 words</span>
              </div>

              {/* Translated Content */}
              <div className="bg-white rounded-lg p-6 min-h-[400px] overflow-auto">
                <div className="text-gray-800">
                  <h1 className="text-2xl font-bold mb-4 text-blue-600">Request for Proposal - Translated</h1>
                  <p className="mb-4">This is the translated document content showing the alignment fixes in action.</p>
                  <h2 className="text-xl font-semibold mb-3 text-blue-700">Project Overview</h2>
                  <p className="mb-4">The alignment issues have been resolved by:</p>
                  <ul className="list-disc list-inside mb-4 space-y-1">
                    <li>Changing panel header containers from min-h-[40px] to min-h-[60px]</li>
                    <li>Removing conflicting min-height constraints from child elements</li>
                    <li>Ensuring consistent flexbox alignment patterns</li>
                    <li>Using proper button sizing with size="sm" variant</li>
                  </ul>
                  <p>The green borders show the fixed containers, yellow shows titles, and blue shows the right-side content.</p>
                </div>
              </div>
            </div>
          </Card>
        </div>

        {/* Verification Results */}
        <Card className="mt-6 bg-slate-800/30 border-slate-700 p-6">
          <h3 className="text-lg font-semibold text-white mb-4">✅ Alignment Fixes Applied</h3>
          <div className="space-y-3 text-slate-300">
            <p><strong>Changes Made:</strong></p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li><strong>Panel Headers:</strong> Changed from <code className="bg-slate-700 px-2 py-1 rounded">min-h-[40px]</code> to <code className="bg-slate-700 px-2 py-1 rounded">min-h-[60px]</code></li>
              <li><strong>Button Container:</strong> Removed <code className="bg-slate-700 px-2 py-1 rounded">min-h-[36px]</code> constraint</li>
              <li><strong>Filename Container:</strong> Removed <code className="bg-slate-700 px-2 py-1 rounded">min-h-[36px]</code> constraint</li>
              <li><strong>Consistent Alignment:</strong> All elements now use proper flexbox <code className="bg-slate-700 px-2 py-1 rounded">items-center</code></li>
            </ul>
            <p><strong>Visual Debugging:</strong></p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li><span className="text-green-400">Green borders:</span> Main header containers (should be perfectly aligned)</li>
              <li><span className="text-yellow-400">Yellow borders:</span> Left side titles</li>
              <li><span className="text-blue-400">Blue borders:</span> Right side content (filename/buttons)</li>
            </ul>
            <p><strong>Expected Result:</strong> Perfect vertical alignment between both panels with no layout shifts during translation states.</p>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default AlignmentVerification;
