<!DOCTYPE html>
<html lang='en'>
<head>
<meta charset='UTF-8'>
<meta name='viewport' content='width=device-width, initial-scale=1.0'>
<title>Translated Document</title>
<style>

        body {
            font-family: 'Segoe UI', Arial, Helvetica, Tahoma, Geneva, Verdana, sans-serif;
            font-size: 17px;
            line-height: 1.8;
            color: #222;
            margin: 0;
            padding: 0;
            background: #f8fafd;
            direction: ltr;
        }
        .document-container {
            padding: 48px 32px 48px 32px;
            max-width: 900px;
            margin: 40px auto 40px auto;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 24px rgba(0,0,0,0.08);
        }
        h1 {
            font-size: 2.8em;
            font-weight: 700;
            color: #003366;
            text-align: center;
            margin-top: 0;
            margin-bottom: 0.7em;
            letter-spacing: 0.02em;
        }
        h2 {
            font-size: 1.7em;
            color: #003366;
            font-weight: 600;
            margin-top: 2.5em;
            margin-bottom: 0.7em;
            border-bottom: 2px solid #e0e6ed;
            padding-bottom: 6px;
            letter-spacing: 0.01em;
        }
        h3 {
            font-size: 1.25em;
            color: #00509e;
            font-weight: 600;
            margin-top: 2em;
            margin-bottom: 0.5em;
        }
        h4, h5, h6 {
            font-size: 1.1em;
            color: #003366;
            font-weight: 500;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }
        p {
            margin: 18px 0;
            color: #333;
            text-align: justify;
            text-justify: inter-word;
        }
        ul, ol {
            margin: 18px 0 18px 36px;
            padding-left: 1.2em;
        }
        ul {
            list-style-type: disc;
        }
        ol {
            list-style-type: decimal;
        }
        ul ul, ol ul {
            list-style-type: circle;
            margin-top: 0;
            margin-bottom: 0;
        }
        ul ol, ol ol {
            list-style-type: lower-latin;
            margin-top: 0;
            margin-bottom: 0;
        }
        li {
            margin-bottom: 0.4em;
            font-size: 1em;
        }
        /* Table container with horizontal scroll for wide tables */
        .table-container {
            width: 100%;
            overflow-x: auto;
            margin: 32px 0;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.07);
            background: #fff;
        }

        table {
            width: 100%;
            min-width: 800px; /* Minimum width to prevent cramping */
            border-collapse: collapse;
            margin: 0;
            background: #fff;
        }

        /* Responsive table sizing based on column count */
        table.wide-table {
            min-width: 1200px; /* For tables with 8+ columns */
        }

        table.extra-wide-table {
            min-width: 1600px; /* For tables with 11+ columns */
        }

        table th, table td {
            border: 1px solid #d1dbe6;
            padding: 12px 8px; /* Reduced padding for wide tables */
            text-align: left;
            vertical-align: top;
            white-space: nowrap; /* Prevent text wrapping in headers */
            min-width: 80px; /* Minimum column width */
        }

        /* Allow text wrapping in description columns */
        table td:nth-child(5), /* Item Description */
        table td:nth-child(6)  /* Specifications */ {
            white-space: normal;
            max-width: 200px;
            min-width: 150px;
        }

        table th {
            background-color: #003366;
            color: #fff;
            font-weight: 700;
            font-size: 1em; /* Slightly smaller for wide tables */
            position: sticky;
            top: 0;
            z-index: 10;
        }

        table tr:nth-child(even) td {
            background-color: #f4f8fb;
        }

        table tr:hover td {
            background-color: #e6f0fa;
        }
        @media (max-width: 1200px) {
            .document-container {
                max-width: 95vw; /* Allow wider container for large tables */
            }
        }

        @media (max-width: 700px) {
            .document-container {
                padding: 16px 4px;
                max-width: 100vw;
            }
            h1 { font-size: 2em; }
            h2 { font-size: 1.2em; }

            /* Mobile table adjustments */
            table th, table td {
                padding: 8px 4px;
                font-size: 0.9em;
            }

            table th {
                font-size: 0.85em;
            }

            /* Reduce minimum widths on mobile */
            table {
                min-width: 600px;
            }

            table.wide-table {
                min-width: 900px;
            }

            table.extra-wide-table {
                min-width: 1200px;
            }
        }
        @media print {
            body, .document-container {
                background: #fff !important;
                box-shadow: none !important;
            }
            .document-container {
                padding: 0;
                margin: 0;
            }
        }
        
</style>
</head>
<body>
<div class='document-container'>
<h1>Appendix No. 2</h1>
<p>GAIT
General Administration
for Information Technology</p>
<h2>Work Program:</h2>
<ol>
<li>Basic Requirements for Implementation</li>
</ol>
<ul>
<li>Provide a cloud infrastructure for the student information system, admission system, and university website, leveraging cloud computing capabilities to ensure high performance and seamless integration.</li>
<li>Migrate the Oracle database to the latest version compatible with performance and stability enhancements.</li>
<li>Migrate the Ellucian Banner 9 environment from King Abdulaziz University servers to the cloud environment.</li>
<li>Migrate the admission system environment from King Abdulaziz University servers to the cloud environment.</li>
<li>Migrate the Camunda environment from King Abdulaziz University servers to the cloud environment.</li>
<li>Migrate the university website environment from King Abdulaziz University servers to the cloud environment.</li>
<li>Create a secure and scalable infrastructure with auto-scaling capabilities to meet future system needs.</li>
<li>Implement security and compliance standards according to the National Cybersecurity Authority (NCA) requirements, ISO 27001, PCI standards, and university policies.</li>
<li>Enhance backup and disaster recovery solutions to ensure zero Recovery Time Objective (RTO) and Recovery Point Objective (RPO).</li>
</ul>
<ol start="2">
<li>Recommended Additional Services</li>
</ol>
<ul>
<li>Implement Infrastructure as Code (IaC) to ensure efficient infrastructure management, reduce operational errors, and achieve operational continuity.</li>
<li>Set up performance monitoring and analysis systems with automatic alerts to ensure system stability and quick response to any issues.</li>
<li>Design a cost optimization framework through resource consumption analysis and providing recommendations for effective allocation.</li>
<li>Develop an automated CI/CD pipeline for seamless application updates and security patch deployment.</li>
<li>Implement data encryption during storage and transit with comprehensive key management to enhance protection.</li>
<li>Create an identity and access management framework based on the principle of least privilege (Least Privilege Access) to ensure precise control over data and system access.</li>
<li>Establish an integrated cloud service governance model including cost management and regulatory compliance control.</li>
<li>Provide training and knowledge transfer programs for the university's IT team to enhance skills in managing and operating the cloud environment.</li>
<li>Conduct advanced performance testing and system tuning to ensure an optimal user experience.</li>
<li>Develop native cloud integration solutions that connect Ellucian Banner with other institutional systems to ensure smooth and efficient operation.</li>
</ul>
<ol start="3">
<li>Security and Compliance Requirements</li>
</ol>
<ul>
<li>Implement comprehensive protection against Distributed Denial of Service (DDoS) attacks at the network and application levels to safeguard systems from cyber threats.</li>
<li>Implement advanced threat intelligence capabilities with automatic response mechanisms for immediate detection and effective handling of attacks.</li>
<li>Set up an integrated Security Information and Event Management (SIEM) system to monitor and analyze suspicious activities in the cloud environment.</li>
<li>Apply an advanced Web Application Firewall (WAF) with custom rules to protect Ellucian Banner, admission, and university website systems from cyber attacks.</li>
<li>Establish advanced network protection mechanisms following a multi-layered defense approach to ensure a secure environment against potential threats.</li>
<li>Implement continuous regulatory compliance monitoring to ensure adherence to legal requirements and internal policies.</li>
<li>Conduct periodic automatic vulnerability scans with immediate remediation procedures to reduce risks.</li>
<li>Design a secure CI/CD pipeline with integrated security tests to ensure secure and vulnerability-free updates.</li>
<li>Implement periodic automatic vulnerability scans using IBM, Trend Micro, Qualys, QRadar tools for compliance analysis, threat tracking, and automatic remediation.</li>
<li>Enforce Multi-Factor Authentication (MFA) for all users to ensure a higher security level when accessing sensitive systems.</li>
<li>Implement precise permission management based on the Role-Based Access Control (RBAC) principle to allocate permissions based on user roles and restrict unauthorized access.</li>
<li>Integrate the NAFATH authentication system using OpenID Connect (OIDC) or SAML 2.0 to ensure secure single sign-on for all users.</li>
<li>Implement VPC Lattice to create a secure service network between different operating environments, enabling centralized and secure service connections without manually managing network complexities.</li>
<li>Use PrivateLink to connect cloud services internally without exposing them to the public internet, enhancing security, reducing cyber risks, and improving performance through private and direct communication channels.</li>
<li>Provide a secure infrastructure that separates virtualization operations from the system runtime environment by moving virtual machine management functions to Dedicated Hardware and reducing reliance on traditional hypervisors, enhancing security by reducing the attack surface and improving performance.</li>
</ul>
<ol start="4">
<li>Project Management and Timeline</li>
</ol>
<ul>
<li>Implement integrated project management across all stages of the transition to the cloud environment to ensure successful execution without disrupting operations.</li>
<li>Develop a detailed Work Breakdown Structure (WBS) with effective resource allocation for all tasks.</li>
<li>Establish a comprehensive project governance framework including a steering committee and clear decision-making procedures.</li>
<li>Implement a risk management plan that includes a thorough risk analysis and mitigation strategies.</li>
<li>Conduct weekly project progress meetings and prepare monthly reports for the executive management to ensure execution transparency.</li>
<li>Prepare and update RAID (Risks, Actions, Issues, Decisions) logs to track project progress and avoid potential obstacles.</li>
<li>Establish effective change management procedures with clear communication plans for all stakeholders.</li>
</ul>
</div>
</body>
</html>