import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Copy, Download } from 'lucide-react';
import Header from '@/components/Header';

const AlignmentTest = () => {
  // Mock data to simulate post-translation state
  const mockFile = {
    name: "Sample_RFP_Document_Very_Long_Filename_Test.pdf"
  };

  const mockTranslatedContent = `
    <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
      <h1 style="color: #2563eb; margin-bottom: 20px;">Request for Proposal - Translation Test</h1>
      <p>This is a sample translated document content to test the alignment issues in the TranslationViewer component.</p>
      <h2 style="color: #1e40af; margin-top: 30px; margin-bottom: 15px;">Project Overview</h2>
      <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
      <ul style="margin: 15px 0; padding-left: 20px;">
        <li>First requirement item</li>
        <li>Second requirement item</li>
        <li>Third requirement item with longer text to test wrapping</li>
      </ul>
      <h2 style="color: #1e40af; margin-top: 30px; margin-bottom: 15px;">Technical Specifications</h2>
      <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
    </div>
  `;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      <Header title="Alignment Test - Translation Viewer">
        <div className="flex items-center gap-4">
          <Button size="sm" variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Home
          </Button>
        </div>
      </Header>

      {/* Content */}
      <div className="max-w-[95vw] mx-auto p-4">
        <div className="grid lg:grid-cols-2 gap-4">
          {/* Original PDF Panel */}
          <Card className="bg-slate-800/50 backdrop-blur-sm border-slate-700 p-4 transition-all duration-200 hover:bg-slate-800/60">
            <div className="space-y-4">
              {/* CRITICAL TEST AREA: Panel Header Alignment */}
              <div className="flex items-center justify-between min-h-[40px] border-2 border-red-500/50">
                <div className="flex items-center border border-yellow-500/50">
                  <h2 className="text-xl font-semibold text-slate-200">Original Document</h2>
                </div>
                <div className="flex items-center gap-2 max-w-[300px] border border-blue-500/50">
                  <div className="bg-slate-700/50 rounded-lg px-3 py-2 border border-slate-600 flex items-center min-h-[36px]">
                    <span className="text-sm text-slate-300 font-medium truncate" title={mockFile.name}>
                      {mockFile.name}
                    </span>
                  </div>
                </div>
              </div>

              {/* PDF Viewer Mock */}
              <div className="bg-slate-900/50 rounded-lg p-4 min-h-[600px] flex items-center justify-center">
                <div className="text-slate-400 text-center">
                  <div className="text-6xl mb-4">📄</div>
                  <p>PDF Viewer Mock</p>
                  <p className="text-sm mt-2">Original document would be displayed here</p>
                </div>
              </div>
            </div>
          </Card>

          {/* Translated Document Panel */}
          <Card className="bg-slate-800/50 backdrop-blur-sm border-slate-700 p-4 transition-all duration-200 hover:bg-slate-800/60">
            <div className="space-y-4">
              {/* CRITICAL TEST AREA: Panel Header Alignment - POST TRANSLATION STATE */}
              <div className="flex items-center justify-between min-h-[40px] border-2 border-red-500/50">
                <div className="flex items-center border border-yellow-500/50">
                  <h2 className="text-xl font-semibold text-slate-200">Translated Document</h2>
                </div>
                <div className="flex items-center gap-2 border border-blue-500/50">
                  <Button size="sm" variant="outline">
                    <Copy className="h-4 w-4 mr-2" />
                    Copy
                  </Button>
                  <Button size="sm" variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Export HTML
                  </Button>
                </div>
              </div>

              {/* Translated Content */}
              <div className="bg-white rounded-lg p-6 min-h-[600px] overflow-auto">
                <div dangerouslySetInnerHTML={{ __html: mockTranslatedContent }} />
              </div>
            </div>
          </Card>
        </div>

        {/* Test Instructions */}
        <Card className="mt-6 bg-slate-800/30 border-slate-700 p-6">
          <h3 className="text-lg font-semibold text-white mb-4">🧪 Alignment Test Instructions</h3>
          <div className="space-y-3 text-slate-300">
            <p><strong>Purpose:</strong> This page simulates the post-translation state to test alignment issues.</p>
            <p><strong>What to check:</strong></p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>Red borders show the main header containers</li>
              <li>Yellow borders show the left side (titles)</li>
              <li>Blue borders show the right side (filename/buttons)</li>
              <li>Check if elements are vertically centered within their containers</li>
              <li>Compare alignment between the two panels</li>
            </ul>
            <p><strong>Expected behavior:</strong> All elements should be perfectly aligned vertically within their colored border containers.</p>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default AlignmentTest;
