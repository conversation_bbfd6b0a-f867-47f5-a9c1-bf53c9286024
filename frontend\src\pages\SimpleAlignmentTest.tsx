import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Copy, Download, ArrowLeft } from 'lucide-react';
import Header from '@/components/Header';

const SimpleAlignmentTest = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      <Header title="Simple Alignment Test">
        <div className="flex items-center gap-4">
          <Button size="sm" variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Home
          </Button>
        </div>
      </Header>

      <div className="p-8">
        <h1 className="text-white text-2xl mb-8">Simple Alignment Test</h1>
      
      {/* Test Case 1: Current Implementation */}
      <div className="mb-12">
        <h2 className="text-white text-lg mb-4">Test Case 1: Current Implementation (min-h-[40px])</h2>
        <div className="bg-slate-800 p-4 rounded-lg">
          <div className="flex items-center justify-between min-h-[40px] bg-red-500/20 border border-red-500">
            <div className="flex items-center bg-yellow-500/20 border border-yellow-500">
              <h3 className="text-xl font-semibold text-slate-200">Translated Document</h3>
            </div>
            <div className="flex items-center gap-2 bg-blue-500/20 border border-blue-500">
              <Button size="sm" variant="outline">
                <Copy className="h-4 w-4 mr-2" />
                Copy
              </Button>
              <Button size="sm" variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Export HTML
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Test Case 2: Fixed Height */}
      <div className="mb-12">
        <h2 className="text-white text-lg mb-4">Test Case 2: Fixed Height (h-[60px])</h2>
        <div className="bg-slate-800 p-4 rounded-lg">
          <div className="flex items-center justify-between h-[60px] bg-red-500/20 border border-red-500">
            <div className="flex items-center bg-yellow-500/20 border border-yellow-500">
              <h3 className="text-xl font-semibold text-slate-200">Translated Document</h3>
            </div>
            <div className="flex items-center gap-2 bg-blue-500/20 border border-blue-500">
              <Button size="sm" variant="outline">
                <Copy className="h-4 w-4 mr-2" />
                Copy
              </Button>
              <Button size="sm" variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Export HTML
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Test Case 3: CSS Grid */}
      <div className="mb-12">
        <h2 className="text-white text-lg mb-4">Test Case 3: CSS Grid</h2>
        <div className="bg-slate-800 p-4 rounded-lg">
          <div className="grid grid-cols-2 items-center min-h-[60px] bg-red-500/20 border border-red-500">
            <div className="flex items-center bg-yellow-500/20 border border-yellow-500">
              <h3 className="text-xl font-semibold text-slate-200">Translated Document</h3>
            </div>
            <div className="flex items-center gap-2 justify-end bg-blue-500/20 border border-blue-500">
              <Button size="sm" variant="outline">
                <Copy className="h-4 w-4 mr-2" />
                Copy
              </Button>
              <Button size="sm" variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Export HTML
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Test Case 4: Explicit Height on Buttons */}
      <div className="mb-12">
        <h2 className="text-white text-lg mb-4">Test Case 4: Explicit Button Heights</h2>
        <div className="bg-slate-800 p-4 rounded-lg">
          <div className="flex items-center justify-between min-h-[60px] bg-red-500/20 border border-red-500">
            <div className="flex items-center h-full bg-yellow-500/20 border border-yellow-500">
              <h3 className="text-xl font-semibold text-slate-200">Translated Document</h3>
            </div>
            <div className="flex items-center gap-2 h-full bg-blue-500/20 border border-blue-500">
              <Button size="sm" variant="outline" className="h-[36px]">
                <Copy className="h-4 w-4 mr-2" />
                Copy
              </Button>
              <Button size="sm" variant="outline" className="h-[36px]">
                <Download className="h-4 w-4 mr-2" />
                Export HTML
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Test Case 5: Flexbox with Stretch */}
      <div className="mb-12">
        <h2 className="text-white text-lg mb-4">Test Case 5: Flexbox with Stretch</h2>
        <div className="bg-slate-800 p-4 rounded-lg">
          <div className="flex items-stretch justify-between min-h-[60px] bg-red-500/20 border border-red-500">
            <div className="flex items-center bg-yellow-500/20 border border-yellow-500">
              <h3 className="text-xl font-semibold text-slate-200">Translated Document</h3>
            </div>
            <div className="flex items-center gap-2 bg-blue-500/20 border border-blue-500">
              <Button size="sm" variant="outline">
                <Copy className="h-4 w-4 mr-2" />
                Copy
              </Button>
              <Button size="sm" variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Export HTML
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Instructions */}
      <div className="bg-slate-800 p-6 rounded-lg">
        <h3 className="text-white text-lg mb-4">🔍 What to Look For:</h3>
        <ul className="text-slate-300 space-y-2">
          <li>• <span className="text-red-400">Red border:</span> Main container</li>
          <li>• <span className="text-yellow-400">Yellow border:</span> Left side (title)</li>
          <li>• <span className="text-blue-400">Blue border:</span> Right side (buttons)</li>
          <li>• Check if the yellow and blue sections are vertically centered within the red container</li>
          <li>• Look for any misalignment between the title and buttons</li>
        </ul>
      </div>
      </div>
    </div>
  );
};

export default SimpleAlignmentTest;
